const glob = require('glob')
const path = require('path')
const { sep } = path

/**
 * router-schema loader
 * @param app koa 实例
 *
 * 通过'json-schema & ajv' 对API规则进行约束，配合 controller-params-verify中间件使用
 *
 * app/router-schema/**.js
 *
 * 输出：
 * app.routerSchema = {
 *     '${api1}': ${jsonSchema1}},
 *     '${api2}': ${jsonSchema2}},
 *     '${api3}': ${jsonSchema3}},
 * }
 */
module.exports = (app) => {
  // 读取 app/router-schema/**/**.js 目录下的所有文件
  const routerSchemaPath = path.resolve(app.businessPath, `.${sep}router-schema`)
  const fileList = glob.sync(path.resolve(routerSchemaPath, `.${sep}**${sep}**.js`))

  // 注册所有routerSchema，使得可以'controller.routerSchema'这样访问
  let routerSchema = {}

  fileList.forEach((file) => {
    routerSchema = {
      ...routerSchema,
      ...require(path.resolve(file))
    }
  })

  app.routerSchema = routerSchema
}
