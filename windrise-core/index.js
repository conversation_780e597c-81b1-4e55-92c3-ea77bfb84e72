const Koa = require('koa')
const path = require('path')
const { sep } = path // 兼容不同操作系统上的斜杠

const env = require('./env')

const middlewareLoader = require('./loader/middleware')
const routerSchemaLoader = require('./loader/router-schema')
const routerLoader = require('./loader/router')
const controllerLoader = require('./loader/controller')
const serviceLoader = require('./loader/service')
const configLoader = require('./loader/config')
const extendLoader = require('./loader/extend')

module.exports = {
  /**
   * 启动项目
   * @param options 项目配置
   *        options = {
   *            name: 项目名,
   *            homePage: 首页
   *        }
   */
  start(options = {}) {
    // koa实例
    const app = new Koa()

    // 应用配置
    app.options = options
    console.log('[ app.options ]', app.options)

    // 基础路径
    app.baseDir = process.cwd()
    console.log('[ app.baseDir ]', app.baseDir)
    // 业务文件路径
    app.businessPath = path.resolve(app.baseDir, `.${sep}app`)
    console.log('[ app.businessPath ]', app.businessPath)

    // 环境变量
    app.env = env()
    console.log('[ -start- env: ]', app.env.get())

    // 加载 middleware
    middlewareLoader(app)
    console.log('[ -start- 加载 middleware 完成 ]', app.middlewares)
    // 加载 routerSchema
    routerSchemaLoader(app)
    console.log('[ -start- 加载 routerSchema 完成 ]', app.routerSchema)
    // 加载 controller
    controllerLoader(app)
    console.log('[ -start- 加载 controller 完成 ]', app.controller)
    // 加载 service
    serviceLoader(app)
    console.log('[ -start- 加载 service 完成 ]')
    // 加载 config
    configLoader(app)
    console.log('[ -start- 加载 config 完成 ]', app.config)
    // 加载 extend
    extendLoader(app)
    console.log('[ -start- 加载 extend 完成 ]', app)

    // 注册全局中间件
    // app/middleware.js
    try {
      require(path.resolve(app.businessPath, `.${sep}middleware.js`))(app)
      console.log('[ -start- 加载全局中间件 完成 ]')
    } catch (e) {
      console.error('[ 加载中间件 报错 ]', e.message)
    }

    // 注册路由
    routerLoader(app)
    console.log('[ -start- 加载 router 完成 ]')

    // 启动服务
    try {
      const port = process.env.PORT || 8080
      const host = process.env.ip || '0.0.0.0'
      app.listen(port, host)

      console.log('[ 服务启动于端口： ]', port)
    } catch (e) {
      console.error('[ 启动服务错误 ]', e)
    }

    console.log(' ~~~~~~~~~~~~~~ ')
    return app
  }
}
