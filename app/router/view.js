/**
 * 导出一个函数，用于配置路由
 *
 * @param {Object} app - 应用实例，提供应用级别的控制器访问
 * @param {Object} router - 路由器实例，用于定义路由规则
 */
module.exports = (app, router) => {
  // 提取视图控制器，以简化后续对视图相关操作的访问
  const { view: viewController } = app.controller

  // 用户输入 http://ip:port/view/xxx 能渲染出对应的页面
  // 这里定义了一个路由规则，处理所有以 '/view/:page' 形式的GET请求
  // ':page' 是一个路由参数，表示请求URL中的动态部分，用于指定具体要渲染的页面
  // viewController.renderPage.bind(viewController) 绑定了视图控制器的上下文，确保在 renderPage 方法中可以访问控制器的属性和方法
  router.get('/view/:page', viewController.renderPage.bind(viewController))
}
