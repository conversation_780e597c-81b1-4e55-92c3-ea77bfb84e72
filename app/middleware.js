const path = require('path')

/**
 * 配置Koa框架的模板引擎
 * @param {Object} app - Koa应用实例
 */
module.exports = (app) => {
  // 配置静态根目录
  const KoaStatic = require('koa-static')
  app.use(KoaStatic(path.resolve(process.cwd(), './app/public')))
  // 模板渲染引擎
  const KoaNunjucks = require('koa-nunjucks-2')

  // 使用KoaNunjucks中间件配置模板引擎
  app.use(
    KoaNunjucks({
      // 模板文件扩展名
      ext: 'tpl',
      // 模板文件所在路径
      path: path.resolve(process.cwd(), './app/public/'),
      // Nunjucks模板引擎配置项
      nunjucksConfig: {
        // 不使用缓存
        nocache: true,
        // 删除块语句的尾随换行符
        trimBlocks: true
      }
    })
  )

  // 引入 ctx.body 解析中间件
  const bodyParser = require('koa-bodyparser')
  app.use(
    bodyParser({
      formList: '1000mb',
      enableTypes: ['json', 'form', 'text']
    })
  )

  // 引入异常捕获中间件
  app.use(app.middlewares.errorHandle)

  // 签名合法性校验
  app.use(app.middlewares.apiSignVerify)

  // 引入API参数校验
  app.use(app.middlewares.apiParamsVerify)
}
