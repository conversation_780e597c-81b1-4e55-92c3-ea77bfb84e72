module.exports = (app) => {
  const BaseController = require('./base')(app)

  return class projectController extends BaseController {
    /**
     * 根据 projKey 获取项目配置
     */
    get(ctx) {
      const { proj_key: projKey } = ctx.request.query
      const { project: projectService } = app.service

      const projConfig = projectService.get(projKey)

      if (!projConfig) {
        this.fail(ctx, '项目不存在', 50000)
        return
      }

      this.success(ctx, projConfig)
    }

    /**
     * 获取当前 projectKey 对应模型下的项目列表（如果无 projectKey 的话，则全量获取）
     */
    getList(ctx) {
      const { proj_key: projKey } = ctx.request.query

      const { project: projectService } = app.service
      const projectList = projectService.getList({ projKey })

      const dtoProjectList = projectList.map((item) => {
        const { modelKey, key, name, desc, homePage } = item

        return {
          modelKey,
          key,
          name,
          desc,
          homePage
        }
      })

      this.success(ctx, dtoProjectList)
    }

    /**
     * 获取所有模型与项目的结构化数据
     */
    async getModelList(ctx) {
      const { project: projectService } = app.service
      const modelList = await projectService.getModelList()

      // 构造返回结果,只返回关键结果
      const dtoModelList = modelList.reduce((preList, curr) => {
        const { model, project } = curr

        // 构造 model 关键数据
        const { key, name, desc } = model
        const dtoModel = { key, name, desc }

        // 构造 project 关键数据
        const dtoProject = Object.keys(project).reduce((preObj, projKey) => {
          const { key, name, desc, homePage } = project[projKey]
          preObj[projKey] = { key, name, desc, homePage }
          return preObj
        }, {})

        // 整合返回结构
        preList.push({
          model: dtoModel,
          project: dtoProject
        })

        return preList
      }, [])

      this.success(ctx, dtoModelList)
    }
  }
}
