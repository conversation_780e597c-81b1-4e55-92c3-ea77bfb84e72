const md5 = require('md5')

/**
 * API 签名合法性校验
 * @param app koa实例
 */
module.exports = (app) => {
  return async (ctx, next) => {
    // 只处理 API 请求做签名校验
    if (ctx.path.indexOf('/controller') < 0) {
      return await next()
    }

    const {
      path,
      method,
      request: { headers }
    } = ctx
    const { s_sign: sSign, s_t: st } = headers

    const signKey = '签名字符串'
    const signature = md5(`${signKey}_${st}`)

    app.logger.info(`[${method} ${path}] signature: ${signature}`)

    if (!sSign || !st || signature !== sSign.toLowerCase() || Date.now() - st > 600 * 1000) {
      ctx.status = 200
      ctx.body = {
        success: false,
        code: 445,
        message: '签名校验失败或超时！'
      }
      return
    }

    await next()
  }
}
