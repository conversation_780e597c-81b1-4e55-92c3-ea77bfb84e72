const Ajv = require('ajv')
const ajv = new Ajv()

/**
 * API 参数校验
 * @param app Koa实例
 */
module.exports = (app) => {
  const $schema = 'http://json-schema.org/draft-07/schema#'

  return async (ctx, next) => {
    // 只对API请求做参数校验
    if (ctx.path.indexOf('api') < 0) {
      return await next()
    }

    // 获取请求参数
    const { body, query, headers } = ctx.request
    const { params, path, method } = ctx

    app.logger.info(`[${method} ${path}] body: ${JSON.stringify(body)}`)
    app.logger.info(`[${method} ${path}] query: ${JSON.stringify(query)}`)
    app.logger.info(`[${method} ${path}] params: ${JSON.stringify(params)}`)
    app.logger.info(`[${method} ${path}] headers: ${JSON.stringify(headers)}`)

    const schema = app.routerSchema[path]?.[method.toLowerCase()]

    if (!schema) {
      return await next()
    }

    let valid = true
    let validator

    // 校验 headers
    if (valid && schema && schema.headers) {
      // 给schema加上版本
      schema.headers.$schema = $schema
      // 使用ajv编译schema.headers，生成一个校验器
      validator = ajv.compile(schema.headers)
      // 使用校验器，校验headers
      valid = validator(headers)
    }

    // 校验 body
    if (valid && schema && schema.body) {
      schema.body.$schema = $schema
      validator = ajv.compile(schema.body)
      valid = validator(body)
    }

    // 校验 query
    if (valid && schema && schema.query) {
      schema.query.$schema = $schema
      validator = ajv.compile(schema.query)
      valid = validator(query)
    }

    // 校验 params
    if (valid && schema && schema.params) {
      schema.params.$schema = $schema
      validator = ajv.compile(schema.params)
      valid = validator(params)
    }

    if (!valid) {
      ctx.status = 200
      ctx.body = {
        success: false,
        code: 442,
        message: `参数校验失败！${ajv.errorsText(validator.errors)}`,
        errors: validator.errors
      }
      return
    }

    await next()
  }
}
