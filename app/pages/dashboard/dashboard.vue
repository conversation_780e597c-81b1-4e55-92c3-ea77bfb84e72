<template>
  <el-config-provider :locale="zhCn">
    <HeaderView :projName="projectName" @menu-select="onMenuSelect">
      <template #main-content>
        <router-view></router-view>
      </template>
    </HeaderView>
  </el-config-provider>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import HeaderView from './complex-view/header-view/header-view.vue'
import $curl from '$common/curl'
import { useMenuStore } from '$store/menu'
import { useProjectStore } from '$store/project'

const route = useRoute()
const router = useRouter()
const menuStore = useMenuStore()
const projectStore = useProjectStore()

const projectName = ref('')

onMounted(() => {
  getProjectList()
  getProjectConfig()
})

// 请求 /api/project/list 接口，并缓存到 project-store 中
const getProjectList = async () => {
  const res = await $curl({
    method: 'get',
    url: '/api/project/list',
    query: {
      proj_key: route.query.proj_key
    }
  })

  if (!res || !res.data || !res.data) {
    return
  }
  projectStore.setProjectList(res.data)
}

// 请求 /api/project 接口，并缓存到 menu-store 中
const getProjectConfig = async () => {
  const res = await $curl({
    method: 'get',
    url: '/api/project',
    query: {
      proj_key: route.query.proj_key
    }
  })

  if (!res || !res.data || !res.data) {
    return
  }
  const { name, menu } = res.data
  projectName.value = name
  menuStore.setMenuList(menu)
}

// 点击菜单回调
const onMenuSelect = (menuItem) => {
  const { moduleType, key, customConfig } = menuItem

  // 如果是当前页面，不处理
  if (key === route.query.key) {
    return
  }

  const pathMap = {
    sider: '/sider',
    iframe: '/iframe',
    schema: '/schema',
    custom: customConfig?.path
  }

  router.push({
    path: pathMap[moduleType],
    query: {
      key,
      proj_key: route.query.proj_key
    }
  })
}
</script>

<style lang="less" scoped>
:deep(.el-main) {
  padding: 0;
  height: 100%;
}
</style>
