<template>
  <SiderContainer>
    <template #menu-content>
      <el-menu :default-active="activeKey" :ellipsis="false" @select="onMenuSelect">
        <template v-for="item in menuList">
          <SubMenu v-if="item.subMenu && item.subMenu.length > 0" :menu-item="item"> </SubMenu>
          <el-menu-item v-else :index="item.key">
            {{ item.name }}
          </el-menu-item>
        </template>
      </el-menu>
    </template>
    <template #main-content>
      <router-view></router-view>
    </template>
  </SiderContainer>
</template>
<script setup>
import { ref, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMenuStore } from '$store/menu'
import SiderContainer from '$widgets/sider-container/sider-container.vue'
import SubMenu from './complex-view/sub-menu/sub-menu.vue'

const route = useRoute()
const router = useRouter()
const menuStore = useMenuStore()

const activeKey = ref('')
const menuList = ref([])

const setActiveKey = () => {
  let siderMenuItem = menuStore.findMenuItem({
    key: 'key',
    value: route.query.sider_key
  })

  // 如果首次加载 sider-view，用户未选中左侧菜单，需要默认选中第一个
  if (!siderMenuItem) {
    const headerMenuItem = menuStore.findMenuItem({
      key: 'key',
      value: route.query.key
    })

    if (headerMenuItem && headerMenuItem.siderConfig && headerMenuItem.siderConfig.menu) {
      const siderMenuList = headerMenuItem.siderConfig.menu
      siderMenuItem = menuStore.findFirstMenuItem(siderMenuList) // 找出左侧菜单中的第一项
      if (siderMenuItem) {
        handleMenuSelect(siderMenuItem.key)
      }
    }
  }

  activeKey.value = siderMenuItem?.key
}
const setMenuList = () => {
  const menuItem = menuStore.findMenuItem({
    key: 'key',
    value: route.query.key
  })

  if (menuItem && menuItem.siderConfig && menuItem.siderConfig.menu) {
    menuList.value = menuItem.siderConfig.menu
  }
}

watch(
  () => route.query.key,
  (key) => {
    setMenuList()
    setActiveKey()
  },
  { deep: true }
)
watch(
  () => menuStore.menuList,
  () => {
    setMenuList()
    setActiveKey()
  },
  {
    deep: true
  }
)

onMounted(() => {
  setMenuList()
  setActiveKey()
})

const handleMenuSelect = (menuKey) => {
  const menuItem = menuStore.findMenuItem({
    key: 'key',
    value: menuKey
  })

  const { key, moduleType, customConfig } = menuItem

  // 如果是当前页面，不处理
  if (key === route.query.sider_key) {
    return
  }

  const pathMap = {
    iframe: '/iframe',
    schema: '/schema',
    custom: customConfig?.path
  }
  router.push({
    path: `/sider${pathMap[moduleType]}`,
    query: {
      key: route.query.key,
      sider_key: menuKey,
      proj_key: route.query.proj_key
    }
  })
}

const onMenuSelect = (menuKey) => {
  handleMenuSelect(menuKey)
}
</script>
<style lang="less" scoped></style>
