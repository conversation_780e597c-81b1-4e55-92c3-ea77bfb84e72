import { ref, watch, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { useMenuStore } from '$store/menu'

// 通用构建 schema 方法
const buildDtoSchema = (_schema, comName) => {
  if (!_schema?.properties) {
    return {}
  }

  const dtoSchema = {
    type: 'object',
    properties: {}
  }
  // 提取有效 schema 字段信息
  for (const key in _schema.properties) {
    // properties 中的子项
    const props = _schema.properties[key]

    // 判断是否有 要获取的配置
    if (props[`${comName}Option`]) {
      let dtoProps = {}

      // 将非 Option 结尾的字段名，直接赋给 dtoProps
      for (const pKey in props) {
        if (pKey.indexOf('Option') < 0) {
          dtoProps[pKey] = props[pKey]
        }
      }
      // 将 comName Option 赋给 dtoProps
      dtoProps = Object.assign({}, dtoProps, { option: props[`${comName}Option`] })
      dtoSchema.properties[key] = dtoProps
    }
  }

  return dtoSchema
}

export const useSchema = () => {
  const route = useRoute()
  const menuStore = useMenuStore()

  const api = ref('')
  const tableSchema = ref({})
  const tableConfig = ref({})

  // 构造 schemaConfig 相关配置，输送给 schemaView 解释。
  const buildData = () => {
    const { key, sider_key: siderKey } = route.query

    const mItem = menuStore.findMenuItem({
      key: 'key',
      value: siderKey ?? key
    })

    if (mItem && mItem.schemaConfig) {
      const { schemaConfig: sConfig } = mItem
      const configSchema = JSON.parse(JSON.stringify(sConfig.schema))

      api.value = sConfig.api ?? ''
      tableSchema.value = {}
      tableConfig.value = undefined

      nextTick(() => {
        tableSchema.value = buildDtoSchema(configSchema, 'table')
        tableConfig.value = sConfig.tableConfig
      })
    }
  }

  watch(
    [() => route.query.key, () => route.query.sider_key, () => menuStore.menuList],
    () => {
      buildData()
    },
    { deep: true }
  )

  onMounted(() => {
    buildData()
  })

  return {
    api,
    tableSchema,
    tableConfig
  }
}
