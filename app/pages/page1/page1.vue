<template>
  <h1>page1</h1>
  <el-input v-model="content" style="width: 300px" />
  <el-table :data="tableData" style="width: 100%">
    <el-table-column prop="name" label="Name" width="180" />
    <el-table-column prop="desc" label="Desc" />
  </el-table>
  <div>{{ content }}</div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import $curl from '$common/curl'

console.log('[ page1 init ]')

const content = ref('')
const tableData = ref([])

onMounted(async () => {
  try {
    const res = await $curl({
      url: '/api/project/list',
      method: 'get',
      query: {
        proj_key: 'test'
      }
    })

    tableData.value = res.data
  } catch (e) {
    //
  }
})
</script>

<style lang="less" scoped>
h1 {
  color: red;
}
</style>
