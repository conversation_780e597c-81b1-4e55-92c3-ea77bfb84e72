const md5 = require('md5')
import { ElMessage } from 'element-plus'

/**
 * 前端封装的 curl 请求
 */
const curl = ({
  // 请求地址
  url,
  // 请求方法
  method = 'post',
  // 请求头
  headers = {},
  // url 参数
  query = {},
  // post body
  data = {},
  // 返回体数据格式
  responseType = 'json',
  // 超时时间
  timeout = 60000,
  // 错误信息提示
  errorMessage = '网络异常'
}) => {
  // 接口签名处理(让接口变动态)
  const signKey = '签名字符串'
  const st = Date.now()

  // 构造请求参数(把参数转换成 axios 参数)
  const ajaxSetting = {
    url,
    method,
    params: query,
    data,
    responseType,
    timeout,
    headers: {
      ...headers,
      s_t: st,
      s_sign: md5(`${signKey}_${st}`)
    }
  }

  return axios
    .request(ajaxSetting)
    .then((response) => {
      const resData = response.data || {}

      // 后端API返回格式
      const { success } = resData

      // 失败
      if (!success) {
        const { message, code } = resData

        if (code === 442) {
          ElMessage.error('请求参数异常')
        } else if (code === 445) {
          ElMessage.error('请求不合法')
        } else if (code === 50000) {
          ElMessage.error(message)
        } else {
          ElMessage.error(errorMessage)
        }

        console.error(message)
        return Promise.resolve({ success, code, message })
      }

      const { data, metadata } = resData
      return Promise.resolve({ success, data, metadata })
    })
    .catch((error) => {
      const { message } = error

      if (message.match(/timeout/)) {
        return Promise.resolve({
          message: '请求超时',
          code: 504
        })
      }

      console.error(error)
      ElMessage.error('操作异常，请联系管理员')
      return Promise.reject(error)
    })
}

export default curl
