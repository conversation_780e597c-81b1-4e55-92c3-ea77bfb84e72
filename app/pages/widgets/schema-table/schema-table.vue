<template>
  <div class="schema-table">
    <el-table v-if="schema && schema.properties" class="table" v-loading :data="tableData">
      <template v-for="(schemaItem, key) in schema.properties">
        <el-table-column v-if="schemaItem.option.visible !== false" :key="key" :prop="key" :label="schemaItem.label" v-bind="schemaItem.option"> </el-table-column>
      </template>
      <el-table-column v-if="buttons?.length > 0" label="操作" fixed="right" :width="operationWidth">
        <template #default="scoped">
          <el-button v-for="item in buttons" link v-bind="item" @click="operationHandler({ btnConfig: item, rowData: scoped.row })">
            {{ item.label }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-row class="pagination">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next. jumper"
        @size-change="onPageSizeChange"
        @current-change="onCurrentPageChange"
      />
    </el-row>
  </div>
</template>
<script setup>
import { ref, toRefs, computed, watch, nextTick, onMounted } from 'vue'
import $curl from '$common/curl'

const emit = defineEmits(['operate'])
const props = defineProps({
  /**
   * schema 配置，结构如下：
   * {
   *   type: 'object',
   *   properties: {
   *     key: {
   *       ...scheme, // 标准 schema 配置
   *       type: '', // 字段类型
   *       label: '', // 字段中文名
   *       // 字段在 table 中的相关配置
   *       option: {
   *         visible: true // 默认为 true (false 或 不配置时，表示不在表单中显示)
   *         ...elTableColumnConfig // 标准的 el-table-column 配置
   *       }
   *     },
   *     ...
   *   }
   * }
   */
  schema: Object,
  /**
   * 表格数据源 API
   */
  api: String,
  /**
   * buttons 操作按钮相关配置，结构如下：
   * [
   *  {
   *    label: '', // 按钮中文名
   *    eventKey: '', // 按钮事件名
   *    eventOption: {} // 按钮事件具体配置
   *    // ...elButtonConfig  // 标准的 el-button 配置
   *  },
   *  ...
   * ]
   */
  buttons: Array
})

const { schema, api, buttons } = toRefs(props)

const operationWidth = computed(() => {
  return buttons?.value?.length > 0
    ? buttons.value.reduce((pre, curr) => {
        return pre + curr.label.length * 18
      }, 50)
    : 50
})

const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(50)
const total = ref(0)

let timeId = null
const loadTableData = async () => {
  if (timeId) {
    clearTimeout(timeId)
  }
  timeId = setTimeout(async () => {
    await fetchTableData()
    timeId = null
  }, 100)
}

const showLoading = () => {
  loading.value = true
}

const hideLoading = () => {
  loading.value = false
}

/**
 * 对后端返回的数据进行渲染前的预处理
 * @param listData 列表数据
 */
const buildTableData = (listData) => {
  if (!schema.value?.properties) {
    return listData
  }

  return listData.map((rowData) => {
    for (const rowKey in rowData) {
      const schemaItem = schema.value.properties[rowKey]

      // 处理 toFixed 配置
      if (schemaItem?.option?.toFixed) {
        rowData[rowKey] = rowData[rowKey]?.toFixed?.(schemaItem.option.toFixed)
      }
    }
  })
}

const fetchTableData = async () => {
  if (!api.value) {
    return
  }

  showLoading()

  // 请求 table 数据
  const res = await $curl({
    method: 'get',
    url: `${api.value}/list`,
    query: {
      page: currentPage.value,
      size: pageSize.value
    }
  })

  hideLoading()

  if (!res || !res.success || !Array.isArray(res.data)) {
    tableData.value = []
    total.value = 0
    return
  }

  tableData.value = buildTableData(res.data)
  total.value = res.metadata.total
}

const initData = () => {
  currentPage.value = 1
  pageSize.value = 50
  nextTick(async () => {
    await loadTableData()
  })
}

watch(
  [schema, api],
  () => {
    initData()
  },
  { deep: true }
)

onMounted(() => {
  initData()
})

const operationHandler = ({ btnConfig, rowData }) => {
  emit('operate', { btnConfig, rowData })
}

const onPageSizeChange = async (value) => {
  pageSize.value = value
  await loadTableData()
}

const onCurrentPageChange = async (value) => {
  currentPage.value = value
  await loadTableData()
}

defineExpose({
  initData,
  loadTableData,
  showLoading,
  hideLoading
})
</script>
<style lang="less" scoped>
.schema-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .table {
    flex: 1;
  }

  .pagination {
    margin: 10px 0;
    text-align: right;
  }
}
</style>
