module.exports = (app) => {
  const BaseService = require('./base')(app)
  const modelList = require('../../model/index')(app)

  return class ProjectService extends BaseService {
    /**
     * 根据 projKey 获取项目配置
     */
    get(projKey) {
      for (const modelItem of modelList) {
        const temp = modelItem.project[projKey]
        if (temp) {
          return temp // 直接返回第一个匹配项
        }
      }
      return undefined // 显式返回 undefined
    }

    /**
     * 获取当前 projectKey 对应模型下的项目列表（如果无 projectKey 的话，则全量获取）
     * @param projKey
     */
    getList({ projKey }) {
      return modelList.reduce((preList, modelItem) => {
        const { project } = modelItem

        // 如果有 projKey 则只取当前同模型下的项目，不传的情况下则取全量
        if (projKey && !project[projKey]) {
          return preList
        }

        for (const pKey in project) {
          preList.push(project[pKey])
        }

        return preList
      }, [])
    }

    /**
     * 获取所有模型与项目的结构化数据
     */
    async getModelList() {
      return modelList
    }
  }
}
