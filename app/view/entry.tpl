<!DOCTYPE html>
<html class="dark">
<head>
    <meta charset="utf-8"/>
    <link href="/static/normalize.css" rel="stylesheet"></link>
    <link href="/static/logo.png" rel="icon" type="image/x-icon"></link>
    <title>{{ name }}</title>
</head>
<body style="margin: 0;">
<div id='root'></div>
<!--<p id="env">{{ env }}</p>-->
<!--<p id="options">{{ options }}</p>-->
<!--<p>{{ baseDir }}</p>-->
</body>
<script type="text/javascript">
    // try {
    //     window.env = document.getElementById('env').innerText;
    //     window.options = JSON.parse(document.getElementById('options').innerText);
    // } catch (e) {
    //     console.log('模板报错', e);
    // }
</script>
</html>
