const path = require('path')
const merge = require('webpack-merge')
const os = require('os')
const HappyPack = require('happypack')

const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin')
const HtmlWebpackInjectAttributesPlugin = require('html-webpack-inject-attributes-plugin')
const TerserWebpackPlugin = require('terser-webpack-plugin')

// 多线程 build 设置
const happypackCommonConfig = {
  debug: true,
  threadPool: HappyPack.ThreadPool({ size: os.cpus().length })
}

// 基类配置
const baseConfig = require('./webpack.base')

// 生产环境 webpack 配置
const webpackConfig = merge.smart(baseConfig, {
  // 指定为生产环境
  mode: 'production',
  // 生产环境的 output 配置
  output: {
    filename: 'js/[name]_[chunkhash:8].bundle.js',
    path: path.join(process.cwd(), './app/public/dist/prod/'),
    publicPath: '/dist/prod/',
    crossOriginLoading: 'anonymous'
  },
  module: {
    rules: [
      {
        test: /\.css$/,
        use: [MiniCssExtractPlugin.loader, 'happypack/loader?id=css']
      },
      {
        test: /\.js$/,
        include: [
          // 只对业务代码进行babel编译，加快 webpack 打包速度
          path.resolve(process.cwd(), './app/pages')
        ],
        use: {
          loader: 'happypack/loader?id=js'
        }
      }
    ]
  },
  // webpack 不会有大量 hints 信息，默认为 waring
  performance: {
    hints: false
  },
  plugins: [
    // 提取 css 的公共部分，有效利用缓存
    new MiniCssExtractPlugin({
      chunkFilename: 'css/[name]_[contenthash:8].bundle.css'
    }),
    // 优化并压缩 css 资源
    new CssMinimizerPlugin(),
    // 多线程打包 js，加快打包速度
    new HappyPack({
      ...happypackCommonConfig,
      id: 'js',
      loaders: [
        {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env'],
            plugins: ['@babel/plugin-transform-runtime']
          }
        }
      ]
    }),
    // 多线程打包 css，加快打包速度
    new HappyPack({
      // 引入公共配置项
      ...happypackCommonConfig,
      // 设置当前实例的标识符为'css'
      id: 'css',
      // 定义用于处理CSS的加载器配置
      loaders: [
        {
          // 指定加载器为'css-loader'，用于处理CSS文件
          loader: 'css-loader',
          // 加载器的选项配置
          options: {
            // 设置importLoaders为1，表示在处理CSS时可以导入其他资源
            importLoaders: 1
          }
        }
      ]
    }),
    // 浏览器在请求资源时，不发送用户的身份凭证
    new HtmlWebpackInjectAttributesPlugin({
      crossorigin: 'anonymous'
    })
  ],
  optimization: {
    // 使用 TerserWebpackPlugin 的并发和缓存，提升压缩阶段的性能
    minimize: true,
    minimizer: [
      new TerserWebpackPlugin({
        // 启用缓存以加快重复构建速度
        cache: true,
        // 启用并发以提高构建性能
        parallel: true,
        // TerserPlugin 的配置选项
        terserOptions: {
          // 压缩选项
          compress: {
            // 移除 console 语句以减小体积
            drop_console: true
          }
        }
      })
    ]
  }
})

module.exports = webpackConfig
