const glob = require('glob')
const path = require('path')
const webpack = require('webpack')
const { VueLoaderPlugin } = require('vue-loader')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const CleanWebpackPlugin = require('clean-webpack-plugin')

// 动态构造 pageEntries htmlWebpackPluginList
const pageEntries = {}
const htmlWebpackPluginList = []

// 获取 app/pages 目录下所有入口文件 (entry.xx.js)
const entryList = path.resolve(process.cwd(), './app/pages/**/entry.*.js')
glob.sync(entryList).forEach((file) => {
  const entryName = path.basename(file, '.js')

  // 构造 entry
  pageEntries[entryName] = file
  // 构造最终渲染的页面文件
  htmlWebpackPluginList.push(
    new HtmlWebpackPlugin({
      // 要注入的代码块
      chunks: [entryName],
      // 指定要使用的模板文件
      template: path.resolve(process.cwd(), './app/view/entry.tpl'),
      // 产物（最终模板）输出路径
      filename: path.resolve(process.cwd(), './app/public/dist/', `${entryName}.tpl`)
    })
  )
})

/**
 * webpack 基础配置
 */
module.exports = {
  // 入口
  entry: pageEntries,
  // 模块解析配置（决定了要加载解析哪些模块，以及用什么方式去解析）
  module: {
    rules: [
      {
        test: /\.vue$/,
        use: {
          loader: 'vue-loader'
        }
      },
      {
        test: /\.js$/,
        include: [
          // 只对业务代码进行babel编译
          path.resolve(process.cwd(), './app/pages')
        ],
        use: {
          loader: 'babel-loader'
        }
      },
      {
        test: /\.(png|jpe?g|gif)(\?.+)?$/,
        use: {
          loader: 'url-loader',
          options: {
            limit: 300,
            esModule: false
          }
        }
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      },
      {
        test: /\.less$/,
        use: ['style-loader', 'css-loader', 'less-loader']
      },
      {
        test: /\.(eot|svg|ttf|woff|woff2)(\?\S*)?$/,
        use: 'file-loader'
      }
    ]
  },
  // 产物输出路径，因为开发和生产黄精输出不一致，所以需要分不同环境配置
  output: {
    filename: 'js/[name]_[chunkhash:8].bundle.js',
    path: path.join(process.cwd(), './app/public/dist/prod'),
    publicPath: '/dist/prod',
    crossOriginLoading: 'anonymous'
  },
  // 配置模块解析的具体行为（定义webpack在打包时，如何找到并解析具体模块的路径）
  resolve: {
    extensions: ['.js', '.vue', '.less', '.css'],
    // 路径别名
    alias: {
      $pages: path.resolve(process.cwd(), './app/pages'),
      $common: path.resolve(process.cwd(), './app/pages/common'),
      $widgets: path.resolve(process.cwd(), './app/pages/widgets'),
      $store: path.resolve(process.cwd(), './app/pages/store')
    }
  },
  // 配置webpack插件
  plugins: [
    // 每次 build 前，清空 public/dist 目录
    // 实例化CleanWebpackPlugin插件，用于在构建前清理指定目录
    new CleanWebpackPlugin(['public/dist'], {
      // 指定需要清理的目录或文件，此处为'public/dist'
      root: path.resolve(process.cwd(), './app/'), // 指定清理的根目录，使用绝对路径
      exclude: [], // 排除不需清理的文件或目录，此处为空数组，表示不排除任何项
      verbose: true, // 设置为true时，将在控制台输出清理过程的信息
      dry: false // 设置为false时，将实际执行清理操作；若为true，则不会真正清理文件
    }),
    // 处理 .view 文件，这个插件是必须的
    // 它的职能是将定义过的其它规则复制并应用到 .view 文件里
    // 例如：有一条匹配规则 /\.js$/ 的规则，那么他会应用到 .view 文件的 <script> 版本
    new VueLoaderPlugin(),
    // 把第三方库暴露到window context下
    new webpack.ProvidePlugin({
      Vue: 'vue',
      axios: 'axios',
      _: 'lodash'
    }),
    // 定义全局常量
    new webpack.DefinePlugin({
      __VUE_OPTIONS_API__: 'true', // 支持vue解析 options controller
      __VUE_PROD_DEVTOOLS__: 'false', // 禁用vue调试工具
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false' // 禁用生产环境显示“水合”信息
    }),
    // 构造最终渲染的页面模板
    ...htmlWebpackPluginList
  ],
  // 配置打包输出优化（配置代码分割，模块合并，缓存，TreeShaking，压缩等优化策略）
  optimization: {
    /**
     * 把 js 文件打包成3中类型
     * 1. vendor: 第三方库 lib 库，基本不会改动，除非依赖版本升级
     * 2. common: 业务组件代码的公共部分抽取出来，改动较少
     * 3. entry.{page}: 不用页面 entry 里的业务组件代码的差异部分，会经常改动
     * 目的：把改动和引用频率不一样的 js 区分出来，以达到更好利用浏览器缓存的效果
     */
    splitChunks: {
      chunks: 'all', // 对同步和异步模块都进行分割
      maxAsyncRequests: 10, // 每次异步加载的最大并行请求数
      maxInitialRequests: 10, // 入口点的最大并行请求数
      cacheGroups: {
        // 第三方依赖库
        vendor: {
          // 匹配 mode_modules 目录下的文件
          test: /[\\/]node_modules[\\/]/,
          // 模块名
          name: 'vendor',
          // 优先级 （数字越大，优先级越高）
          priority: 20,
          // 是否强制执行
          enforce: true,
          // 复用已有公共 chunk
          reuseExistingChunk: true
        },
        // 公共模块
        common: {
          // 模块名称
          name: 'common',
          // 被两处引用即被归为公共模块
          minChunks: 2,
          // 公共模块最小体积 (1 byte)
          minSize: 1,
          // 优先级
          priority: 10,
          // 是否复用已有公共 chunk
          reuseExistingChunk: true
        }
      }
    },
    // 将 webpack 运行时生成的代码打包到 runtime.js
    runtimeChunk: true
  }
}
