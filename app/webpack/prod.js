const webpack = require('webpack')
const webProdConfig = require('./config/webpack.prod.js')

console.log('\n 构建中... \n')

webpack(webProdConfig, (err, stats) => {
  if (err) {
    console.log('[ err ]', err)
    return
  }

  process.stdout.write(
    `${stats.toString({
      colors: true, // 在控制台输出色彩信息
      modules: false, // 不显示每个模块的打包信息
      children: false, // 不显示子编译的信息
      chunks: false, // 不显示每个块的打包信息
      chunkModules: true //  显示块的模块信息
    })}\n`
  )
})
