const _ = require('lodash')
const glob = require('glob')
const path = require('path')
const { sep } = path

/**
 *  project 继承 model
 * @param model - 模型配置对象
 * @param project - 项目配置对象
 */
const projectExtendModel = (model, project) => {
  return _.mergeWith({}, model, project, (modelValue, projValue) => {
    // 处理数组合并的特殊情况
    if (Array.isArray(modelValue) && Array.isArray(projValue)) {
      let result = []

      /**
       * 因为 project 继承 model, 所以需要处理修改和新增内容的情况
       * model有的键值，project也有， => 修改 (重载)
       * model有的键值，proj没有 => 保留 (继承)
       * project有的键值，model没有 => 新增 (拓展)
       */

      // 处理修改和保留
      for (let i = 0; i < modelValue.length; i++) {
        let modelItem = modelValue[i]
        const projItem = projValue.find((projItem) => projItem.key === modelItem.key)
        // project有的键值，model也有，则递归调用 projectExtendModel 方法覆盖修改
        result.push(projItem ? projectExtendModel(modelItem, projItem) : modelItem)
      }

      // 处理新增
      for (let i = 0; i < projValue.length; i++) {
        let projItem = projValue[i]
        const modelItem = modelValue.find((modelItem) => modelItem.key === projItem.key)

        // model没有的键值，project有，则直接新增
        if (!modelItem) {
          result.push(projItem)
        }
      }

      return result
    }
  })
}

/**
 * 解析 model 配置，并返回组织且继承后的数据结构。
 *
 * 该函数会遍历指定目录下的所有模型文件和项目配置文件，
 * 根据文件路径解析出模型和项目的键（key），并构建一个包含模型及其关联项目的列表。
 * 每个模型对象包含一个 `modelKey` 和一个 `project` 对象，
 * `project` 对象中包含了与该模型相关的所有项目的键值对。
 * [{
 *   model: ${model}
 *   project: {
 *     proj1Key: ${proj1},
 *     proj2Key: ${proj2},
 *   }
 * },
 *  ...
 * ]
 *
 * @param {Object} app - 应用实例对象，包含应用程序的基本信息，如根目录等。
 * @returns {Array<Object>} 返回一个数组，每个元素是一个对象，包含以下属性：
 *   - modelKey: 模型的唯一标识符
 *   - project: 包含该项目下所有子项目的键值对的对象
 */
module.exports = (app) => {
  const modelList = []

  // 获取模型目录的绝对路径
  const modelPath = path.resolve(app.baseDir, `.${sep}model`)

  // 使用 glob 同步读取model目录下的所有 .js 文件
  const fileList = glob.sync(path.resolve(modelPath, `.${sep}**${sep}**.js`))

  fileList.forEach((file) => {
    // 跳过 index.js 文件，避免重复加载
    if (file.indexOf('index.js') > -1) {
      return
    }

    // 根据文件路径区分是model配置还是project配置
    const type = file.indexOf(`${sep}project${sep}`) > -1 ? 'project' : 'model'

    if (type === 'project') {
      // 解析出model和project的键，并构建project配置
      const modelKey = file.match(/\/model\/(.*?)\/project/)?.[1]
      const projKey = file.match(/\/project\/(.*?)\.js/)?.[1]

      let modelItem = modelList.find((item) => item.model?.key === modelKey)
      if (!modelItem) {
        // 如果当前model不存在于 modelList 中，则创建新的model项
        modelItem = {}
        modelList.push(modelItem)
      }
      if (!modelItem.project) {
        // 初始化项目对象
        modelItem.project = {}
      }

      // 加载项目配置文件并设置其键
      modelItem.project[projKey] = require(path.resolve(file))
      modelItem.project[projKey].key = projKey // 注入 projKey
      modelItem.project[projKey].modelKey = modelKey // 注入 modelKey
    }

    if (type === 'model') {
      // 解析出model的键，并构建model配置
      const modelKey = file.match(/\/model\/(.*?)\/model\.js/)?.[1]
      let modelItem = modelList.find((item) => item.model?.key === modelKey)
      if (!modelItem) {
        // 如果当前model不存在于 modelList 中，则创建新的model项
        modelItem = {}
        modelList.push(modelItem)
      }

      // 加载model配置文件并设置其键
      modelItem.model = require(path.resolve(file))
      modelItem.model.key = modelKey
    }
  })

  // 数据进一步 整理 project => 继承model
  modelList.forEach((item) => {
    const { model, project } = item

    for (const key in project) {
      project[key] = projectExtendModel(model, project[key])
    }
  })

  // 返回构造好的model及其关联项目的列表
  return modelList
}
