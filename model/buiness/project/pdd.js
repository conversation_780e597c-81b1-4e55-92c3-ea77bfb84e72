module.exports = {
  name: '拼多多',
  desc: '拼多多电商系统',
  homePage: '/todo?proj_key=pdd&key=product',
  menu: [
    {
      key: 'product',
      name: '商品管理(拼多多)'
    },
    {
      key: 'client',
      name: '客户管理(拼多多)',
      moduleType: 'schema',
      schemaConfig: {
        api: '/api/client',
        schema: {
          type: 'object',
          properties: {
            key: {
              type: 'input',
              label: '名字',
              tableOption: {
                visible: true,
                c: 3,
                d: 4
              },
              formOption: {
                visible: true
              }
            },
            key2: {
              type: 'input',
              label: '年龄',
              tableOption: {
                visible: true,
                a: 1,
                b: 2
              }
            }
          }
        },
        tableConfig: {
          headerButtons: [],
          rowButtons: []
        }
      }
    },
    {
      key: 'data',
      name: '数据分析',
      menuType: 'module',
      moduleType: 'sider',
      siderConfig: {
        menu: [
          {
            key: 'analysis',
            name: '电商罗盘',
            menuType: 'module',
            moduleType: 'custom',
            customConfig: {
              path: '/todo'
            }
          },
          {
            key: 'sider-search',
            name: '信息查询',
            menuType: 'module',
            moduleType: 'iframe',
            iframeConfig: {
              path: 'https://juejin.cn'
            }
          },
          {
            key: 'categories',
            name: '分类数据',
            menuType: 'group',
            subMenu: [
              {
                key: 'categories-1',
                name: '一级分类',
                menuType: 'module',
                moduleType: 'custom',
                customConfig: {
                  path: '/todo'
                }
              },
              {
                key: 'categories-2',
                name: '二级分类',
                menuType: 'module',
                moduleType: 'iframe',
                iframeConfig: {
                  path: 'https://juejin.cn'
                }
              },
              {
                key: 'tags',
                name: '标签',
                menuType: 'module',
                moduleType: 'schema',
                schemaConfig: {
                  api: '/api/client',
                  schema: {}
                }
              }
            ]
          }
        ]
      }
    },
    {
      key: 'search',
      name: '信息查询',
      menuType: 'module',
      moduleType: 'iframe',
      iframeConfig: {
        path: 'https://juejin.cn'
      }
    }
  ]
}
