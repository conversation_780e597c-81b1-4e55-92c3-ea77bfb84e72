{"name": "windrise", "version": "1.0.0", "description": "# 全栈实现一个企业级应用", "main": "index.js", "scripts": {"lint": "eslint --quiet --ext js,vue . --fix", "test": "_ENV='local' mocha 'test/**/*.js'", "dev": "_ENV='local' nodemon ./index.js", "beta": "_ENV='beta' node ./index.js", "prod": "_ENV='production' node ./index.js", "dev:win": "set _ENV='local' && nodemon ./index.js", "beta:win": "set _ENV='beta' && node ./index.js", "prod:win": "set _ENV='production' && node ./index.js", "build:dev": "node --max_old_space_size=4096 ./app/webpack/dev.js", "build:prod": "node ./app/webpack/prod.js"}, "repository": {"type": "git", "url": "https://e.coding.net/g-lwqa7624/windrise/windrise.git"}, "author": "breeze", "license": "ISC", "dependencies": {"@babel/core": "^7.24.0", "@element-plus/icons-vue": "^2.3.1", "ajv": "^6.10.2", "axios": "^0.19.2", "echarts": "^5.5.0", "element-plus": "^2.3.7", "generate-password": "^1.7.1", "glob": "^7.1.4", "jsonwebtoken": "^9.0.2", "knex": "^0.19.0", "koa": "2.7.0", "koa-bodyparser": "^4.2.1", "koa-nunjucks-2": "^3.0.2", "koa-router": "^7.4.0", "koa-static": "^5.0.0", "koa-useragent": "2.0.0", "koa2-cors": "^2.0.6", "less": "^3.8.1", "lodash": "^4.17.21", "log4js": "^6.9.1", "md5": "^2.2.1", "moment": "^2.29.4", "mysql": "^2.18.1", "node-schedule": "^2.1.1", "nodemon": "^1.19.2", "path": "^0.12.7", "pinia": "^2.1.6", "superagent": "^8.1.2", "vue": "^3.3.4", "vue-json-viewer": "^3.0.4", "vue-router": "^4.2.4", "vuex": "^4.1.0"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.1.0", "@babel/preset-env": "^7.4.5", "assert": "^2.0.0", "babel-eslint": "^10.0.2", "babel-loader": "^8.0.4", "clean-webpack-plugin": "^0.1.19", "consoler": "^0.2.0", "css-loader": "^0.23.1", "css-minimizer-webpack-plugin": "^5.0.1", "directory-named-webpack-plugin": "^4.0.1", "eslint": "^7.32.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-vue": "^9.17.0", "express": "^4.18.2", "file-loader": "^6.2.0", "ghooks": "~1.0.3", "happypack": "^5.0.1", "html-webpack-inject-attributes-plugin": "^1.0.1", "html-webpack-plugin": "^5.5.3", "less-loader": "^11.1.3", "mini-css-extract-plugin": "^2.7.6", "mocha": "^6.1.4", "prettier": "^3.5.1", "style-loader": "^0.14.1", "supertest": "^4.0.2", "terser-webpack-plugin": "^2.3.5", "url-loader": "^4.1.1", "validate-commit-msg": "~2.14.0", "vue-loader": "^17.2.2", "vue-style-loader": "^4.1.2", "webpack": "^5.88.1", "webpack-dev-middleware": "^6.1.1", "webpack-hot-middleware": "^2.25.4", "webpack-merge": "^4.2.1"}, "config": {"ghooks": {"commit-msg": "validate-commit-msg", "pre-commit": "npm run lint"}}}