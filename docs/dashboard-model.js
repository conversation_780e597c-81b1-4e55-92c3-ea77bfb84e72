const a = {
  mode: 'dashboard', // 模板类型，不同模板类型对应不一样的模板数据结构
  name: '', // 名称
  desc: '', // 描述
  icon: '', // 图标
  homePage: '', // 首页(项目配置)
  menu: [
    // menuItem
    {
      key: '', // 唯一描述
      name: '', // 菜单名称
      menuType: '', // 枚举值，group / module

      // 当 menuType == group 时，可填
      subMenu: [
        {
          // 可递归 menuItem
        }
      ],

      // 当 menuType == module 时，可填
      moduleType: '', // 枚举值：sider / iframe / custom / schema
      // 当 moduleType == sider 时
      siderConfig: {
        menu: [
          {
            // 可递归 menuItem（除 moduleType === sider）
          }
        ]
      },
      // 当 moduleType == iframe 时
      iframeConfig: {
        path: '' // iframe 路径
      },
      // 当 moduleType == custom 时
      customConfig: {
        path: '' // 自定义组件路由
      },
      // 当 moduleType == schema 时
      schemaConfig: {
        api: '/controller/user', // 数据源api （遵循 RESTFUL 规范）
        // 板块数据结构
        schema: {
          type: 'object',
          properties: {
            key: {
              // ...scheme, // 标准 schema 配置
              type: '', // 字段类型
              label: '', // 字段中文名
              // 字段在 table 中的相关配置
              tableOption: {
                toFixed: 2, // 保留小数后几位
                visible: true // 默认为 true (false 或 不配置时，表示不在表单中显示)
                // ...elTableColumnConfig // 标准的 el-table-column 配置
              }
            },
            key2: {
              type: '',
              label: '',
              tableOption: {
                visible: true
                // ...elTableColumnConfig
              }
            }
            // ...
          }
        },
        tableConfig: {
          headerButtons: [
            {
              label: '', // 按钮中文名
              eventKey: '', // 按钮事件名
              eventOption: {} // 按钮事件具体配置
              // ...elButtonConfig  // 标准的 el-button 配置
            }
            // ...
          ],
          rowButtons: [
            {
              label: '', // 按钮中文名
              eventKey: '', // 按钮事件名
              eventOption: {} // 按钮事件具体配置
              // ...elButtonConfig  // 标准的 el-button 配置
            }
            // ...
          ]
        }, // table 相关配置
        searchConfig: {}, // search-bar 相关配置
        components: {} // 模板组件
      }
    }
  ]
}
